package com.yunmai.controller;

import com.yunmai.domain.ParkingRecord;
import com.yunmai.service.ParkingRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/records")
@Tag(name = "停车记录管理", description = "包含车辆进出记录相关接口")
public class ParkingRecordController {
    @Autowired
    private ParkingRecordService recordService;

    @Operation(summary = "根据车牌号查询记录", description = "返回指定车牌的所有停车记录")
    @Parameter(name = "plateNumber", description = "车牌号码", required = true, example = "京A12345")
    @GetMapping("/plate/{plateNumber}")
    public List<ParkingRecord> getByPlateNumber(@PathVariable String plateNumber) {
        return recordService.getByPlateNumber(plateNumber);
    }

    @Operation(summary = "按时间范围查询记录")
    @Parameters({
            @Parameter(name = "startTime", description = "开始时间(yyyy-MM-dd HH:mm:ss)", example = "2023-01-01 00:00:00"),
            @Parameter(name = "endTime", description = "结束时间(yyyy-MM-dd HH:mm:ss)", example = "2023-01-02 23:59:59")
    })
    @GetMapping("/getParkRecord")
    public List<ParkingRecord> getParkRecord(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        return recordService.getParkRecord(startTime, endTime);
    }

    @PostMapping("/batch")
    public boolean addBatch() {
        List<ParkingRecord> pgRecords = recordService.getPGRecords();
        // 获取实际入库的数据
        LocalDateTime startDateTime = LocalDateTime.now().minusDays(3);
        LocalDateTime endDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.of("Asia/Shanghai"));
        String startTime = startDateTime.format(formatter);
        String endTime = endDateTime.format(formatter);
        List<ParkingRecord> parkRecord = getParkRecord(startTime, endTime);

        List<String> existKeys = parkRecord.stream()
                .map(this::buildUniqueKey)
                .collect(Collectors.toList());
        // 步骤2：分批处理（成功/失败分离）
        List<ParkingRecord> newRecords = pgRecords.stream()
                .filter(r -> !existKeys.contains(buildUniqueKey(r)))
                .collect(Collectors.toList());
        return recordService.saveParkRecord(newRecords);
    }

    private String buildUniqueKey(ParkingRecord r) {
        // 防御性编程
        if (r == null) return "";

        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.of("Asia/Shanghai"));
        // 统一时间处理（保留到秒级精度）
        String timeStr = Optional.ofNullable(r.getTime())
                .map(t -> t.truncatedTo(ChronoUnit.SECONDS)) // 去除毫秒
                .map(t -> t.format(formatter))
                .orElse("");

        // 标准化车牌号（去空格/统一大小写）
        String plate = Optional.ofNullable(r.getVhPNumber())
                .map(String::trim)
                .map(String::toUpperCase)
                .orElse("");

        return plate + "|" + timeStr + "|" + r.getDirection();
    }
}