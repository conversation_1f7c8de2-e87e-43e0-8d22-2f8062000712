package com.yunmai.controller;

import com.yunmai.domain.KqSource;
import com.yunmai.service.KqSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/kq")
@Slf4j
public class KqSourceController {

    @Autowired
    private KqSourceService kqSourceService;

    @PostMapping("/save")
    public Boolean saveKqSource(@RequestBody List<KqSource> records) {
        try {
            boolean result = kqSourceService.saveKqSource(records);
            return result ? true : false;
        } catch (Exception e) {
            log.error("保存考勤数据失败", e);
            return false;
        }
    }
}
