package com.yunmai.controller.consume;

import com.yunmai.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Builder;
import lombok.Data;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 停车缴费控制器
 * 负责处理与停车费用支付相关的请求和逻辑
 *
 * <AUTHOR>
 * @since 2023-07-01
 */
@Validated // 启用参数校验
@RestController
@RequestMapping("/api/parking")
@Tag(name = "停车缴费管理", description = "包含车辆进出缴费相关接口，支持车牌识别、费用计算和支付处理")
public class ParkingConsumeController {

    /**
     * 处理车辆缴费请求
     *
     * @param vehicleNo 车牌号码，格式要求：汉字开头 + 大写字母 + 5-6位数字/字母（支持新能源车牌）
     * @param money     支付金额（单位：元），范围在0.01到9999.99之间
     * @param visitor   是否为访客车牌（默认false）
     * @return 统一封装的处理结果，包含支付详细信息
     */
    @PostMapping("/consume")
    @Operation(
            summary = "车辆缴费接口",
            description = "基于车牌号完成停车费用支付流程，支持访客车牌折扣处理。<br><br>"
//                    +
//                    "主要处理步骤：<br>" +
//                    "1. 验证车牌有效性<br>" +
//                    "2. 查询当前停车记录<br>" +
//                    "3. 计算实际应付金额<br>" +
//                    "4. 进行支付处理<br>" +
//                    "5. 更新停车记录状态"
            ,
            parameters = {
                    @Parameter(
                            name = "vehicleNo",
                            in = ParameterIn.QUERY,
                            description = "车牌号码（支持常规车牌和新能源车牌）<br><br>" +
                                    "格式规范：<br>" +
                                    "- 第一位：省份简称汉字<br>" +
                                    "- 第二位：发牌机关代号字母（A-Z）<br>" +
                                    "- 后续：5~6位字母或数字组合",
                            required = true,
                            schema = @Schema(
                                    type = "string",
                                    pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                                    example = "粤B12345"
                            )
                    ),
                    @Parameter(
                            name = "money",
                            in = ParameterIn.QUERY,
                            description = "支付金额（人民币）<br><br>" +
                                    "规则说明：<br>" +
                                    "- 最小值：¥0.01元<br>" +
                                    "- 最大值：¥9,999.99元",
                            required = true,
                            schema = @Schema(
                                    type = "number",
                                    format = "double",
                                    minimum = "0.01",
                                    maximum = "9999.99",
                                    example = "15.50"
                            )
                    ),
                    @Parameter(
                            name = "visitor",
                            in = ParameterIn.QUERY,
                            description = "访客车牌标识<br><br>" +
                                    "业务规则：<br>" +
                                    "- true：访客车牌享受20%折扣<br>" +
                                    "- false：普通车牌（默认）",
                            required = false,
                            schema = @Schema(
                                    type = "boolean",
                                    defaultValue = "false",
                                    example = "false"
                            )
                    )
            }
    )
    public CommonResult<PaymentResult> consume(
            // 车牌号参数校验
            @RequestParam("vehicleNo")
            @Pattern(regexp = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                    message = "车牌号格式错误：需符合[汉字][大写字母][5-6位字母/数字]格式")
            String vehicleNo,

            // 金额参数校验
            @RequestParam("money")
            @DecimalMin(value = "0.01", message = "金额不能低于最低支付额(0.01元)")
            @DecimalMax(value = "9999.99", message = "金额超过系统最大金额限制(9999.99元)")
            BigDecimal money,

            // 访客车牌标识（默认false）
            @RequestParam(value = "visitor", required = false, defaultValue = "false")
            boolean visitor) {

        /*
         * 实际业务逻辑实现流程：
         * 1. 车牌有效性校验（示例保留）
         * 2. 获取当前停车记录（从数据库/缓存）
         * 3. 基于停车时长、车型、节假日等计算费用
         * 4. 访客车牌应用折扣策略
         * 5. 调用支付网关完成支付
         * 6. 更新停车记录支付状态
         * 7. 生成支付凭证并返回
         */

        // ========== 当前模拟实现（用于开发测试）==========
        // 创建模拟支付结果（开发阶段使用）
        PaymentResult result = PaymentResult.builder()
                .vehicleNo(vehicleNo)
                .paidAmount(money)
                .payTime(LocalDateTime.now())
                .transactionId("SIM" + System.currentTimeMillis()) // 模拟交易号
                .build();

        // 添加访客标识信息
        if(visitor) {
            result.setRemark("访客车牌：享受20%优惠折扣");
        }

        return CommonResult.success(result);
    }

    // ==============================
    // 内部数据结构定义
    // ==============================

    /**
     * 支付结果数据模型
     * 用于封装支付成功后返回的详细信息
     */
    @Data
    @Builder
    @Schema(description = "支付结果信息")
    static class PaymentResult {
        /**
         * 车牌号码（加密处理）
         */
        @Schema(
                description = "车牌号码（前端显示脱敏处理）",
                example = "粤B****5"
        )
        private String vehicleNo;

        /**
         * 实际支付金额
         */
        @Schema(
                description = "实际支付金额（单位：元）",
                example = "15.50"
        )
        private BigDecimal paidAmount;

        /**
         * 支付完成时间
         */
        @Schema(
                description = "支付完成时间（系统自动记录）",
                example = "2023-07-20T14:30:45"
        )
        private LocalDateTime payTime;

        /**
         * 支付交易流水号
         */
        @Schema(
                description = "支付系统生成的交易流水号（唯一标识）",
                example = "PAY2023072012453678901"
        )
        private String transactionId;

        /**
         * 附加备注信息
         */
        @Schema(
                description = "支付相关附加信息（如折扣说明）",
                example = "访客车牌：享受20%优惠折扣"
        )
        private String remark;
    }
}