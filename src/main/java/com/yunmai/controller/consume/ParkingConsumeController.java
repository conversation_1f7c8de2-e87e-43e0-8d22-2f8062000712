package com.yunmai.controller.consume;

import com.yunmai.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Builder;
import lombok.Data;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 停车缴费控制器
 * 负责处理与停车费用支付相关的请求和逻辑
 *
 * <AUTHOR>
 * @since 2023-07-01
 */
@Validated // 启用参数校验
@RestController
@RequestMapping("/api/parking")
@Tag(name = "停车缴费管理", description = "包含车辆进出缴费相关接口，支持车牌识别、费用计算和支付处理")
public class ParkingConsumeController {

    /**
     * 处理车辆缴费请求
     */
    @PostMapping("/consume")
    @Operation(
            summary = "车辆缴费接口",
            description = "基于车牌号完成停车费用支付流程，支持访客车牌折扣处理",
            parameters = {
                    @Parameter(
                            name = "vehicleNo",
                            in = ParameterIn.QUERY,
                            description = "车牌号码（支持常规车牌和新能源车牌）",
                            required = true,
                            schema = @Schema(
                                    type = "string",
                                    pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                                    example = "粤B12345"
                            )
                    ),
                    @Parameter(
                            name = "money",
                            in = ParameterIn.QUERY,
                            description = "支付金额（人民币）",
                            required = true,
                            schema = @Schema(
                                    type = "number",
                                    format = "double",
                                    minimum = "0.01",
                                    maximum = "9999.99",
                                    example = "15.50"
                            )
                    ),
                    @Parameter(
                            name = "visitor",
                            in = ParameterIn.QUERY,
                            description = "访客车牌标识",
                            required = false,
                            schema = @Schema(
                                    type = "boolean",
                                    defaultValue = "false",
                                    example = "false"
                            )
                    )
            }
    )
    public CommonResult<PaymentResult> consume(
            @Pattern(regexp = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                    message = "车牌号格式错误：需符合[汉字][大写字母][5-6位字母/数字]格式")
            @RequestParam("vehicleNo") String vehicleNo,

            @DecimalMin(value = "0.01", message = "金额不能低于最低支付额(0.01元)")
            @DecimalMax(value = "9999.99", message = "金额超过系统最大金额限制(9999.99元)")
            @RequestParam("money") BigDecimal money,

            @RequestParam(value = "visitor", required = false, defaultValue = "false") boolean visitor) {

        // 模拟支付实现
        PaymentResult result = PaymentResult.builder()
                .vehicleNo(vehicleNo)
                .paidAmount(money)
                .payTime(LocalDateTime.now())
                .transactionId("PAY" + System.currentTimeMillis()) // 模拟交易号
                .build();

        if(visitor) {
            result.setRemark("访客车牌：享受20%优惠折扣");
        }

        return CommonResult.success(result);
    }

    /**
     * 查询车牌账户信息
     */
    @GetMapping("/plate/{plateNumber}")
    @Operation(
            summary = "查询车牌账户信息",
            description = "根据车牌号查询账户余额信息",
            parameters = {
                    @Parameter(
                            name = "plateNumber",
                            in = ParameterIn.PATH,
                            description = "标准车牌号码",
                            required = true,
                            schema = @Schema(
                                    type = "string",
                                    pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                                    example = "京A12345"
                            )
                    )
            }
    )
    public CommonResult<AccountInfo> getByPlateNumberMoney(
            @Pattern(regexp = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                    message = "车牌号格式无效")
            @PathVariable String plateNumber) {

        // 模拟账户查询
        AccountInfo account = createRandomAccount(plateNumber);
        return CommonResult.success(account);
    }

    /**
     * 查询访客车辆
     */



    /**
     * 查询订单接口
     * 根据交易流水号查询订单详细信息
     */
    @GetMapping("/order/{transactionId}")
    @Operation(
            summary = "查询支付订单",
            description = "根据交易流水号查询支付订单的详细信息",
            parameters = {
                    @Parameter(
                            name = "transactionId",
                            in = ParameterIn.PATH,
                            description = "交易流水号（支付时返回）",
                            required = true,
                            schema = @Schema(
                                    type = "string",
                                    minLength = 10,
                                    maxLength = 32,
                                    example = "PAY1690000000001"
                            )
                    )
            }
    )
    public CommonResult<OrderDetail> queryOrder(
            @NotBlank(message = "交易流水号不能为空")
            @Pattern(regexp = "^[A-Z0-9]{10,32}$", message = "无效的交易流水号格式")
            @PathVariable String transactionId) {

        // 模拟订单查询
        OrderDetail order = createMockOrderDetail(transactionId);

        // 实际业务应根据 transactionId 从数据库中查询订单
        // OrderDetail order = orderService.queryByTransactionId(transactionId);

        return CommonResult.success(order);
    }

    /**
     * 创建模拟订单信息
     */
    private OrderDetail createMockOrderDetail(String transactionId) {
        Random random = new Random();

        String[] plates = {"粤B12345", "京A88888", "浙C66666", "鄂A456WJ", "琼D78901"};
        String[] payMethods = {"微信支付", "支付宝", "银联支付", "现金", "ETC自动扣费"};
        String[] payStatuses = {"支付成功", "支付中", "支付失败", "已退款"};

        return OrderDetail.builder()
                .transactionId(transactionId)
                .plateNumber(plates[random.nextInt(plates.length)])
                .paidAmount(new BigDecimal(10 + random.nextInt(100) + random.nextDouble())
                        .setScale(2, BigDecimal.ROUND_HALF_UP))
                .payTime(LocalDateTime.now().minusMinutes(random.nextInt(120)))
                .payStatus(payStatuses[random.nextInt(payStatuses.length)])
//                .payMethod(payMethods[random.nextInt(payMethods.length)])
//                .parkingDuration(minutesToDuration(30 + random.nextInt(600)))
                .build();
    }

    /**
     * 将分钟数转换为可读的时长格式
     */
    private String minutesToDuration(int minutes) {
        long hours = minutes / 60;
        long mins = minutes % 60;
        long days = hours / 24;
        hours = hours % 24;

        StringBuilder sb = new StringBuilder();
        if (days > 0) sb.append(days).append("天");
        if (hours > 0) sb.append(hours).append("小时");
        if (mins > 0) sb.append(mins).append("分钟");
        return sb.toString();
    }

    /**
     * 为新用户创建随机账户信息
     */
    private AccountInfo createRandomAccount(String plateNumber) {
        Random random = new Random();
        BigDecimal balance = new BigDecimal(random.nextInt(1000) + random.nextDouble())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        return new AccountInfo(plateNumber, balance);
    }

    // ======================
    // 内部数据结构定义
    // ======================

    /**
     * 支付结果模型
     */
    @Data
    @Builder
    @Schema(description = "支付结果信息")
    static class PaymentResult {
        @Schema(description = "车牌号码", example = "粤B****5")
        private String vehicleNo;

        @Schema(description = "实际支付金额", example = "15.50")
        private BigDecimal paidAmount;

        @Schema(description = "支付完成时间", example = "2023-07-20T14:30:45")
        private LocalDateTime payTime;

        @Schema(description = "支付交易流水号", example = "PAY2023072012453678901")
        private String transactionId;

        @Schema(description = "附加备注信息", example = "访客车牌：享受20%优惠折扣")
        private String remark;
    }

    /**
     * 账户信息模型
     */
    @Schema(description = "车辆账户详细信息")
    static class AccountInfo {
        @Schema(description = "车牌号码", example = "京A12345")
        private final String plateNumber;

        @Schema(description = "账户余额(元)", example = "325.75")
        private final BigDecimal balance;

        @Schema(description = "创建时间", example = "2023-07-01T10:30:00")
        private final LocalDate createDate = LocalDate.now();

        public AccountInfo(String plateNumber, BigDecimal balance) {
            this.plateNumber = plateNumber;
            this.balance = balance;
        }

        public String getPlateNumber() { return plateNumber; }
        public BigDecimal getBalance() { return balance; }
        public LocalDate getCreateDate() { return createDate; }
    }

    /**
     * 订单详情模型
     */
    @Data
    @Builder
    @Schema(description = "订单详细信息")
    static class OrderDetail {
        @Schema(description = "交易流水号", example = "PAY2023072012453678901")
        private String transactionId;

        @Schema(description = "车牌号码", example = "粤B12345")
        private String plateNumber;

        @Schema(description = "支付金额", example = "15.50")
        private BigDecimal paidAmount;

        @Schema(description = "支付时间", example = "2023-07-20T14:30:45")
        private LocalDateTime payTime;

        @Schema(description = "支付状态",
                allowableValues = {"支付成功", "支付中", "支付失败", "已退款"},
                example = "支付成功")
        private String payStatus;
    }
}