package com.yunmai.controller.consume;

import com.yunmai.domain.ParkingRecord;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Validated
@RestController
@RequestMapping("/api/parking")
@Tag(name = "停车缴费管理", description = "包含车辆进出缴费相关接口")
public class ParkingConsumeController {

    @PostMapping("/consume")
    @Operation(
            summary = "车辆缴费接口",
            description = "根据车牌号、金额和当前时间计算停车费用，支持访客车牌识别",
            parameters = {
                    @Parameter(
                            name = "vehicleNo",
                            in = ParameterIn.QUERY,
                            description = "车牌号码（支持常规车牌和新能源车牌)",
                            required = true,
                            schema = @Schema(
                                    type = "string",
                                    pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                                    example = "粤B12345"
                            )
                    ),
                    @Parameter(
                            name = "money",
                            in = ParameterIn.QUERY,
                            description = "缴费金额（单位：元）",
                            required = true,
                            schema = @Schema(
                                    type = "number",
                                    format = "double",
                                    minimum = "0.01",
                                    maximum = "9999.99",
                                    example = "15.50"
                            )
                    ),
                    @Parameter(
                            name = "visitor",
                            in = ParameterIn.QUERY,
                            description = "是否访客车牌（默认false）",
                            required = false,
                            schema = @Schema(type = "boolean", defaultValue = "false")
                    )
            }
    )
    public ResponseEntity<ApiResponse<PaymentResult>> consume(
            @RequestParam("vehicleNo")
            @Pattern(regexp = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                    message = "车牌号格式错误")
            String vehicleNo,

            @RequestParam("money")
            @DecimalMin(value = "0.01", message = "金额不能小于0.01元")
            @DecimalMax(value = "9999.99", message = "金额不能超过9999.99元")
            BigDecimal money,

            @RequestParam(value = "visitor", required = false, defaultValue = "false")
            boolean visitor,

            @RequestParam(value = "payType", required = false, defaultValue = "WECHAT")
            PayType payType) {

        // 1. 参数基础校验 (通过Spring Validation自动完成)

//        // 2. 访客车牌验证（示例）
//        if (visitor && !isValidVisitorPlate(vehicleNo)) {
//            throw new BusinessException("访客车牌未登记");
//        }
//
//        // 3. 获取当前停车信息（实际业务需要对接DAO或服务）
//        ParkingRecord record = parkingService.getCurrentParkingRecord(vehicleNo);
//        if (record == null) {
//            throw new NotFoundException("未找到停车记录");
//        }
//
//        // 4. 计算应缴费用（示例逻辑）
//        BigDecimal payableAmount = calculatePayableAmount(record, visitor, LocalDateTime.now());
//
//        // 5. 金额一致性验证
//        if (money.compareTo(payableAmount) != 0) {
//            throw new BusinessException("支付金额与实际金额不符：" + payableAmount);
//        }
//
//        // 6. 执行支付操作（对接支付系统）
//        String transactionId = paymentService.processPayment(vehicleNo, money, payType);
//
//        // 7. 更新停车记录状态
//        record.setPaid(true);
//        record.setPaymentTime(LocalDateTime.now());
//        parkingService.updateRecord(record);

        // 8. 构建响应数据
        PaymentResult result = PaymentResult.builder()
                .vehicleNo(vehicleNo)
                .paidAmount(money)
                .payTime(LocalDateTime.now())
//                .transactionId(transactionId)
//                .gateNo(record.getExitGate())
                .paymentType(payType.name())
                .build();

        return ResponseEntity.ok(
                new ApiResponse<>(200, "支付成功", result)
        );
    }

    // 示例方法：访客车牌验证
    private boolean isValidVisitorPlate(String vehicleNo) {
        // 实际实现应查询访客登记系统
        return true;
//        return visitorPlateService.exists(vehicleNo);
    }

    // 示例方法：费用计算逻辑
    private BigDecimal calculatePayableAmount(ParkingRecord record, boolean visitor, LocalDateTime exitTime) {
//        // 基础停车费计算
//        BigDecimal baseFee = parkingFeeCalculator.calculate(record.getEntryTime(), exitTime);
//
//        // 访客折扣逻辑
//        if (visitor) {
//            return baseFee.multiply(BigDecimal.valueOf(0.8)); // 20%折扣
//        }

//        return baseFee;
        return new BigDecimal(0);
    }

    // 响应数据封装
    @Data
    @Builder
    private static class PaymentResult {
        private String vehicleNo;
        private BigDecimal paidAmount;
        private LocalDateTime payTime;
        private String transactionId;
        private String gateNo;
        private String paymentType;
    }

    // 统一响应结构
    @Data
    @AllArgsConstructor
    private static class ApiResponse<T> {
        private int code;
        private String message;
        private T data;
    }

    // 支付方式枚举
    private enum PayType {
        WECHAT, ALIPAY, BANK
    }
}
