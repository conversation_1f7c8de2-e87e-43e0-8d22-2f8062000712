package com.yunmai.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 访客车牌管理服务
 */
@Service
public class VisitorPlateService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String VISITOR_PLATE_PREFIX = "visitor:plate:";
    private static final String VISITOR_PLATE_SET = "visitor:plates:all";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 注册访客车牌
     * @param plateNumber 车牌号
     * @param validHours 有效时长（小时）
     * @param visitorName 访客姓名
     * @param contactPhone 联系电话
     * @return 是否注册成功
     */
    public boolean registerVisitorPlate(String plateNumber, int validHours, String visitorName, String contactPhone) {
        if (plateNumber == null || plateNumber.trim().isEmpty()) {
            return false;
        }

        String key = VISITOR_PLATE_PREFIX + plateNumber.toUpperCase();
        LocalDateTime expireTime = LocalDateTime.now().plusHours(validHours);
        
        // 构建访客信息
        VisitorInfo visitorInfo = new VisitorInfo();
        visitorInfo.setPlateNumber(plateNumber.toUpperCase());
        visitorInfo.setVisitorName(visitorName);
        visitorInfo.setContactPhone(contactPhone);
        visitorInfo.setRegisterTime(LocalDateTime.now());
        visitorInfo.setExpireTime(expireTime);
        
        String infoJson = toJson(visitorInfo);
        
        // 存储到Redis，设置过期时间
        redisTemplate.opsForValue().set(key, infoJson, validHours, TimeUnit.HOURS);
        
        // 添加到访客车牌集合
        redisTemplate.opsForSet().add(VISITOR_PLATE_SET, plateNumber.toUpperCase());
        
        return true;
    }

    /**
     * 验证访客车牌是否有效
     * @param plateNumber 车牌号
     * @return 是否有效
     */
    public boolean isValidVisitorPlate(String plateNumber) {
        if (plateNumber == null || plateNumber.trim().isEmpty()) {
            return false;
        }

        String key = VISITOR_PLATE_PREFIX + plateNumber.toUpperCase();
        return redisTemplate.hasKey(key);
    }

    /**
     * 获取访客车牌信息
     * @param plateNumber 车牌号
     * @return 访客信息
     */
    public VisitorInfo getVisitorInfo(String plateNumber) {
        if (plateNumber == null || plateNumber.trim().isEmpty()) {
            return null;
        }

        String key = VISITOR_PLATE_PREFIX + plateNumber.toUpperCase();
        String infoJson = redisTemplate.opsForValue().get(key);
        
        if (infoJson == null) {
            return null;
        }
        
        return fromJson(infoJson);
    }

    /**
     * 注销访客车牌
     * @param plateNumber 车牌号
     * @return 是否注销成功
     */
    public boolean unregisterVisitorPlate(String plateNumber) {
        if (plateNumber == null || plateNumber.trim().isEmpty()) {
            return false;
        }

        String key = VISITOR_PLATE_PREFIX + plateNumber.toUpperCase();
        
        // 从Redis中删除
        Boolean deleted = redisTemplate.delete(key);
        
        // 从访客车牌集合中移除
        redisTemplate.opsForSet().remove(VISITOR_PLATE_SET, plateNumber.toUpperCase());
        
        return deleted != null && deleted;
    }

    /**
     * 获取所有有效的访客车牌
     * @return 访客车牌集合
     */
    public Set<String> getAllValidVisitorPlates() {
        return redisTemplate.opsForSet().members(VISITOR_PLATE_SET);
    }

    /**
     * 延长访客车牌有效期
     * @param plateNumber 车牌号
     * @param additionalHours 延长的小时数
     * @return 是否延长成功
     */
    public boolean extendVisitorPlate(String plateNumber, int additionalHours) {
        if (plateNumber == null || plateNumber.trim().isEmpty()) {
            return false;
        }

        String key = VISITOR_PLATE_PREFIX + plateNumber.toUpperCase();
        
        if (!redisTemplate.hasKey(key)) {
            return false;
        }
        
        // 延长过期时间
        return redisTemplate.expire(key, additionalHours, TimeUnit.HOURS);
    }

    /**
     * 清理过期的访客车牌（从集合中移除）
     */
    public void cleanExpiredVisitorPlates() {
        Set<String> allPlates = redisTemplate.opsForSet().members(VISITOR_PLATE_SET);
        
        if (allPlates != null) {
            for (String plate : allPlates) {
                String key = VISITOR_PLATE_PREFIX + plate;
                if (!redisTemplate.hasKey(key)) {
                    // 如果Redis中没有这个key，说明已过期，从集合中移除
                    redisTemplate.opsForSet().remove(VISITOR_PLATE_SET, plate);
                }
            }
        }
    }

    /**
     * 简单的JSON序列化（实际项目中建议使用Jackson或Gson）
     */
    private String toJson(VisitorInfo info) {
        return String.format("{\"plateNumber\":\"%s\",\"visitorName\":\"%s\",\"contactPhone\":\"%s\",\"registerTime\":\"%s\",\"expireTime\":\"%s\"}",
                info.getPlateNumber(),
                info.getVisitorName() != null ? info.getVisitorName() : "",
                info.getContactPhone() != null ? info.getContactPhone() : "",
                info.getRegisterTime().format(FORMATTER),
                info.getExpireTime().format(FORMATTER));
    }

    /**
     * 简单的JSON反序列化
     */
    private VisitorInfo fromJson(String json) {
        try {
            // 简单解析JSON（实际项目中建议使用Jackson或Gson）
            VisitorInfo info = new VisitorInfo();
            
            String plateNumber = extractJsonValue(json, "plateNumber");
            String visitorName = extractJsonValue(json, "visitorName");
            String contactPhone = extractJsonValue(json, "contactPhone");
            String registerTime = extractJsonValue(json, "registerTime");
            String expireTime = extractJsonValue(json, "expireTime");
            
            info.setPlateNumber(plateNumber);
            info.setVisitorName(visitorName.isEmpty() ? null : visitorName);
            info.setContactPhone(contactPhone.isEmpty() ? null : contactPhone);
            info.setRegisterTime(LocalDateTime.parse(registerTime, FORMATTER));
            info.setExpireTime(LocalDateTime.parse(expireTime, FORMATTER));
            
            return info;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JSON字符串中提取值
     */
    private String extractJsonValue(String json, String key) {
        String pattern = "\"" + key + "\":\"";
        int start = json.indexOf(pattern);
        if (start == -1) return "";
        
        start += pattern.length();
        int end = json.indexOf("\"", start);
        if (end == -1) return "";
        
        return json.substring(start, end);
    }

    /**
     * 访客信息类
     */
    public static class VisitorInfo {
        private String plateNumber;
        private String visitorName;
        private String contactPhone;
        private LocalDateTime registerTime;
        private LocalDateTime expireTime;

        // Getters and Setters
        public String getPlateNumber() { return plateNumber; }
        public void setPlateNumber(String plateNumber) { this.plateNumber = plateNumber; }

        public String getVisitorName() { return visitorName; }
        public void setVisitorName(String visitorName) { this.visitorName = visitorName; }

        public String getContactPhone() { return contactPhone; }
        public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

        public LocalDateTime getRegisterTime() { return registerTime; }
        public void setRegisterTime(LocalDateTime registerTime) { this.registerTime = registerTime; }

        public LocalDateTime getExpireTime() { return expireTime; }
        public void setExpireTime(LocalDateTime expireTime) { this.expireTime = expireTime; }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expireTime);
        }
    }
}
