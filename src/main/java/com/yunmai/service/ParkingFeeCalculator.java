package com.yunmai.service;

import com.yunmai.domain.ParkingRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

/**
 * 停车费用计算服务
 */
@Service
public class ParkingFeeCalculator {

    @Value("${parking.fee.hourly-rate:5.00}")
    private BigDecimal hourlyRate;

    @Value("${parking.fee.free-minutes:15}")
    private Integer freeMinutes;

    @Value("${parking.fee.visitor-discount:0.8}")
    private BigDecimal visitorDiscount;

    @Value("${parking.fee.max-daily-fee:50.00}")
    private BigDecimal maxDailyFee;

    @Value("${parking.fee.min-fee:2.00}")
    private BigDecimal minFee;

    /**
     * 计算停车费用
     * @param entryTime 入场时间
     * @param exitTime 出场时间
     * @param isVisitor 是否访客
     * @return 应付费用
     */
    public BigDecimal calculateFee(LocalDateTime entryTime, LocalDateTime exitTime, boolean isVisitor) {
        if (entryTime == null || exitTime == null) {
            throw new IllegalArgumentException("入场时间和出场时间不能为空");
        }

        if (exitTime.isBefore(entryTime)) {
            throw new IllegalArgumentException("出场时间不能早于入场时间");
        }

        // 计算停车时长（分钟）
        Duration duration = Duration.between(entryTime, exitTime);
        long totalMinutes = duration.toMinutes();

        // 免费时长内不收费
        if (totalMinutes <= freeMinutes) {
            return BigDecimal.ZERO;
        }

        // 计算收费时长
        long chargeableMinutes = totalMinutes - freeMinutes;
        
        // 按小时计费，不足1小时按1小时计算
        long chargeableHours = (chargeableMinutes + 59) / 60; // 向上取整

        // 计算基础费用
        BigDecimal baseFee = hourlyRate.multiply(BigDecimal.valueOf(chargeableHours));

        // 应用每日最高费用限制
        if (baseFee.compareTo(maxDailyFee) > 0) {
            baseFee = maxDailyFee;
        }

        // 应用最低费用
        if (baseFee.compareTo(minFee) < 0 && totalMinutes > freeMinutes) {
            baseFee = minFee;
        }

        // 访客折扣
        if (isVisitor) {
            baseFee = baseFee.multiply(visitorDiscount);
        }

        // 保留两位小数
        return baseFee.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算停车费用（使用ParkingRecord）
     * @param record 停车记录
     * @param exitTime 出场时间
     * @param isVisitor 是否访客
     * @return 应付费用
     */
    public BigDecimal calculateFee(ParkingRecord record, LocalDateTime exitTime, boolean isVisitor) {
        if (record == null || record.getTime() == null) {
            throw new IllegalArgumentException("停车记录或入场时间不能为空");
        }

        // 将OffsetDateTime转换为LocalDateTime
        LocalDateTime entryTime = record.getTime().atZoneSameInstant(ZoneOffset.systemDefault()).toLocalDateTime();
        
        return calculateFee(entryTime, exitTime, isVisitor);
    }

    /**
     * 计算停车时长（分钟）
     * @param entryTime 入场时间
     * @param exitTime 出场时间
     * @return 停车时长（分钟）
     */
    public long calculateParkingDuration(LocalDateTime entryTime, LocalDateTime exitTime) {
        if (entryTime == null || exitTime == null) {
            return 0;
        }
        
        Duration duration = Duration.between(entryTime, exitTime);
        return duration.toMinutes();
    }

    /**
     * 计算停车时长（使用ParkingRecord）
     * @param record 停车记录
     * @param exitTime 出场时间
     * @return 停车时长（分钟）
     */
    public long calculateParkingDuration(ParkingRecord record, LocalDateTime exitTime) {
        if (record == null || record.getTime() == null) {
            return 0;
        }

        LocalDateTime entryTime = record.getTime().atZoneSameInstant(ZoneOffset.systemDefault()).toLocalDateTime();
        return calculateParkingDuration(entryTime, exitTime);
    }

    /**
     * 获取费用计算详情
     * @param entryTime 入场时间
     * @param exitTime 出场时间
     * @param isVisitor 是否访客
     * @return 费用计算详情
     */
    public FeeCalculationDetail calculateFeeDetail(LocalDateTime entryTime, LocalDateTime exitTime, boolean isVisitor) {
        long totalMinutes = calculateParkingDuration(entryTime, exitTime);
        long chargeableMinutes = Math.max(0, totalMinutes - freeMinutes);
        long chargeableHours = (chargeableMinutes + 59) / 60;
        
        BigDecimal baseFee = hourlyRate.multiply(BigDecimal.valueOf(chargeableHours));
        BigDecimal finalFee = calculateFee(entryTime, exitTime, isVisitor);
        
        return FeeCalculationDetail.builder()
                .totalMinutes(totalMinutes)
                .freeMinutes(Math.min(totalMinutes, freeMinutes))
                .chargeableMinutes(chargeableMinutes)
                .chargeableHours(chargeableHours)
                .hourlyRate(hourlyRate)
                .baseFee(baseFee)
                .isVisitor(isVisitor)
                .visitorDiscount(isVisitor ? visitorDiscount : BigDecimal.ONE)
                .finalFee(finalFee)
                .build();
    }

    /**
     * 费用计算详情
     */
    public static class FeeCalculationDetail {
        private long totalMinutes;
        private long freeMinutes;
        private long chargeableMinutes;
        private long chargeableHours;
        private BigDecimal hourlyRate;
        private BigDecimal baseFee;
        private boolean isVisitor;
        private BigDecimal visitorDiscount;
        private BigDecimal finalFee;

        public static FeeCalculationDetailBuilder builder() {
            return new FeeCalculationDetailBuilder();
        }

        // Getters and setters
        public long getTotalMinutes() { return totalMinutes; }
        public void setTotalMinutes(long totalMinutes) { this.totalMinutes = totalMinutes; }
        
        public long getFreeMinutes() { return freeMinutes; }
        public void setFreeMinutes(long freeMinutes) { this.freeMinutes = freeMinutes; }
        
        public long getChargeableMinutes() { return chargeableMinutes; }
        public void setChargeableMinutes(long chargeableMinutes) { this.chargeableMinutes = chargeableMinutes; }
        
        public long getChargeableHours() { return chargeableHours; }
        public void setChargeableHours(long chargeableHours) { this.chargeableHours = chargeableHours; }
        
        public BigDecimal getHourlyRate() { return hourlyRate; }
        public void setHourlyRate(BigDecimal hourlyRate) { this.hourlyRate = hourlyRate; }
        
        public BigDecimal getBaseFee() { return baseFee; }
        public void setBaseFee(BigDecimal baseFee) { this.baseFee = baseFee; }
        
        public boolean isVisitor() { return isVisitor; }
        public void setVisitor(boolean visitor) { isVisitor = visitor; }
        
        public BigDecimal getVisitorDiscount() { return visitorDiscount; }
        public void setVisitorDiscount(BigDecimal visitorDiscount) { this.visitorDiscount = visitorDiscount; }
        
        public BigDecimal getFinalFee() { return finalFee; }
        public void setFinalFee(BigDecimal finalFee) { this.finalFee = finalFee; }

        public static class FeeCalculationDetailBuilder {
            private FeeCalculationDetail detail = new FeeCalculationDetail();

            public FeeCalculationDetailBuilder totalMinutes(long totalMinutes) {
                detail.setTotalMinutes(totalMinutes);
                return this;
            }

            public FeeCalculationDetailBuilder freeMinutes(long freeMinutes) {
                detail.setFreeMinutes(freeMinutes);
                return this;
            }

            public FeeCalculationDetailBuilder chargeableMinutes(long chargeableMinutes) {
                detail.setChargeableMinutes(chargeableMinutes);
                return this;
            }

            public FeeCalculationDetailBuilder chargeableHours(long chargeableHours) {
                detail.setChargeableHours(chargeableHours);
                return this;
            }

            public FeeCalculationDetailBuilder hourlyRate(BigDecimal hourlyRate) {
                detail.setHourlyRate(hourlyRate);
                return this;
            }

            public FeeCalculationDetailBuilder baseFee(BigDecimal baseFee) {
                detail.setBaseFee(baseFee);
                return this;
            }

            public FeeCalculationDetailBuilder isVisitor(boolean isVisitor) {
                detail.setVisitor(isVisitor);
                return this;
            }

            public FeeCalculationDetailBuilder visitorDiscount(BigDecimal visitorDiscount) {
                detail.setVisitorDiscount(visitorDiscount);
                return this;
            }

            public FeeCalculationDetailBuilder finalFee(BigDecimal finalFee) {
                detail.setFinalFee(finalFee);
                return this;
            }

            public FeeCalculationDetail build() {
                return detail;
            }
        }
    }
}
