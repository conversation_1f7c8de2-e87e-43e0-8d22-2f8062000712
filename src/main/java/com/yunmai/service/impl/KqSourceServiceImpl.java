package com.yunmai.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunmai.domain.KqSource;
import com.yunmai.mapper.KqSourceMapper;
import com.yunmai.service.KqSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class KqSourceServiceImpl extends ServiceImpl<KqSourceMapper, KqSource> implements KqSourceService {

    @Override
    @DS("sqlserver")  // 指定使用SQL Server数据源
    public boolean saveKqSource(List<KqSource> records) {
        return saveBatch(records, 1000);
    }
}
