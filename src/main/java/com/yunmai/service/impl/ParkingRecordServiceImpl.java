package com.yunmai.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunmai.domain.ParkingRecord;
import com.yunmai.mapper.ParkingRecordMapper;
import com.yunmai.service.ParkingRecordService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class ParkingRecordServiceImpl extends ServiceImpl<ParkingRecordMapper, ParkingRecord> implements ParkingRecordService{

    @Override
    @DS("mysql")
    public List<ParkingRecord> getByPlateNumber(String plateNumber) {
        return lambdaQuery()
                .eq(ParkingRecord::getVhPNumber, plateNumber)
                .list();
    }

    @Override
    public List<ParkingRecord> getPGRecords() {
        //获取进出数据
        List<ParkingRecord> allRecord = new ArrayList<>();
        List<ParkingRecord> byTimeRangePg1 = getBaseMapper().selectByTimeRangePg1();
        List<ParkingRecord> byTimeRangePg2 = getBaseMapper().selectByTimeRangePg2();
        List<ParkingRecord> byTimeRangePg3 = getBaseMapper().selectByTimeRangePg3();
        allRecord.addAll(byTimeRangePg1);
        allRecord.addAll(byTimeRangePg2);
        allRecord.addAll(byTimeRangePg3);
        allRecord.forEach(i -> i.setId(null));
        return allRecord;
    }

    @Override
    public List<ParkingRecord> getParkRecord(String startTime,String endTime) {
        return getBaseMapper().selectByTimeRangeMQ(startTime, endTime);
    }

    // 插入必须指定MySQL数据源
    @DS("mysql")
    public boolean saveParkRecord(List<ParkingRecord> records) {
        return saveBatch(records, 1000);
    }
}
