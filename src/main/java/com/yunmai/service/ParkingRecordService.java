package com.yunmai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunmai.domain.ParkingRecord;

import java.util.List;

public interface ParkingRecordService extends IService<ParkingRecord> {

    List<ParkingRecord> getByPlateNumber(String plateNumber);

    List<ParkingRecord> getPGRecords();

    List<ParkingRecord> getParkRecord(String startTime,String endTime);

    boolean saveParkRecord(List<ParkingRecord> records);
}
