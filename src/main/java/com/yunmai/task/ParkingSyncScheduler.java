package com.yunmai.task;

import com.yunmai.domain.ParkingRecord;
import com.yunmai.config.redis.DistributedLocker;
import com.yunmai.service.ParkingRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ParkingSyncScheduler {

    private final DistributedLocker locker;

    private final ParkingRecordService parkingRecordService;

    /**
     * 每5分钟同步一次（支持动态配置）
     */
//    @Scheduled(cron = "0 */1 * * * ?")
    public void scheduledSync() {
        String lockKey = "parking:sync:lock";
        try {
            if (locker.tryLock(lockKey, TimeUnit.MINUTES, 4, 5)) {
                log.info("开始执行停车记录同步...");
                List<ParkingRecord> pgRecords = parkingRecordService.getPGRecords();
                // 获取实际入库的数据
                LocalDateTime startDateTime = LocalDateTime.now().minusDays(3);
                LocalDateTime endDateTime = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter
                        .ofPattern("yyyy-MM-dd HH:mm:ss")
                        .withZone(ZoneId.of("Asia/Shanghai"));
                String startTime = startDateTime.format(formatter);
                String endTime = endDateTime.format(formatter);
                List<ParkingRecord> parkRecord = parkingRecordService.getParkRecord(startTime, endTime);

                List<String> existKeys = parkRecord.stream()
                        .map(this::buildUniqueKey)
                        .collect(Collectors.toList());
                // 步骤2：分批处理（成功/失败分离）
                List<ParkingRecord> newRecords = pgRecords.stream()
                        .filter(r -> !existKeys.contains(buildUniqueKey(r)))
                        .collect(Collectors.toList());
                boolean record = parkingRecordService.saveParkRecord(newRecords);
                log.info("同步 {} 条记录,状态{}", newRecords.size(), record);
            }
        } catch (Exception e) {
            log.error("同步任务执行失败", e);
        } finally {
            locker.unlock(lockKey);
        }
    }


    private String buildUniqueKey(ParkingRecord r) {
        // 防御性编程
        if (r == null) return "";

        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.of("Asia/Shanghai"));
        // 统一时间处理（保留到秒级精度）
        String timeStr = Optional.ofNullable(r.getTime())
                .map(t -> t.truncatedTo(ChronoUnit.SECONDS)) // 去除毫秒
                .map(t -> t.format(formatter))
                .orElse("");

        // 标准化车牌号（去空格/统一大小写）
        String plate = Optional.ofNullable(r.getVhPNumber())
                .map(String::trim)
                .map(String::toUpperCase)
                .orElse("");

        return plate + "|" + timeStr + "|" + r.getDirection();
    }
}
