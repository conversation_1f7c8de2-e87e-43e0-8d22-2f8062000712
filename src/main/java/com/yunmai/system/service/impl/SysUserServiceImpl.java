package com.yunmai.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunmai.common.core.domain.entity.SysUser;
import com.yunmai.mapper.SysUserMapper;
import com.yunmai.system.service.ISysUserService;
import org.springframework.stereotype.Service;

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser>  implements ISysUserService {

    @Override
    public SysUser selectUserByUserName(String userName) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<SysUser>();
        queryWrapper.eq(SysUser::getUserName, userName);
        SysUser user = this.getOne(queryWrapper);
        if (user != null) {
            return user;
        }
        return null;
    }
}
