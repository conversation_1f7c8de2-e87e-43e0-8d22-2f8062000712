package com.yunmai.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yunmai.common.core.domain.entity.SysUser;
import com.yunmai.domain.KqSource;

/**
 * 用户
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

}
