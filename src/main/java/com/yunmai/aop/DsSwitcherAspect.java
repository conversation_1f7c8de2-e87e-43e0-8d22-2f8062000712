//package com.ymiots.aop;
//
//import com.ymiots.config.DynamicDataSourceContextHolder;
//import com.ymiots.config.DsSwitcher;
//import com.ymiots.enums.DataSourceEnum;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.*;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.core.Ordered;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
//import java.util.Objects;
//
//@Component
//@Aspect
//@Order(Ordered.LOWEST_PRECEDENCE-1) // 优先级低于@DS
//public class DsSwitcherAspect {
//
//
//    @Pointcut("@annotation(com.ymiots.config.DsSwitcher)")
//    public void dsPointCut() {
//    }
//
//
//    /**
//     * 根据注解改变数据源
//     */
//    @Around("dsPointCut()")
//    private Object around(ProceedingJoinPoint point) throws Throwable {
//        MethodSignature signature =
//                (MethodSignature) point.getSignature();
//        DsSwitcher dsSwitcher =
//                signature.getMethod().getAnnotation(DsSwitcher.class);
//        if (!Objects.isNull(dsSwitcher) && !Objects.isNull(dsSwitcher.value())) {
//            DataSourceEnum annoEnum = dsSwitcher.value();
//            if (DataSourceEnum.PG == annoEnum) {
//                DynamicDataSourceContextHolder.setDataSourceType(DataSourceEnum.PG);
//            } else if (DataSourceEnum.MYSQL == annoEnum) {
//                DynamicDataSourceContextHolder.setDataSourceType(DataSourceEnum.MYSQL);
//            }
//        }
//        try {
//            return point.proceed();
//        } finally {
//            DynamicDataSourceContextHolder.clearDataSourceType();
//        }
//    }
//
//}