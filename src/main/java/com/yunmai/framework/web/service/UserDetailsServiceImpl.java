//package com.yunmai.framework.web.service;
//
//import com.yunmai.common.core.domain.entity.SysUser;
//import com.yunmai.common.exception.ServiceException;
//import com.yunmai.common.utils.MessageUtils;
//import com.yunmai.common.utils.StringUtils;
//import com.yunmai.system.service.ISysUserService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.security.core.userdetails.UserDetailsService;
//import org.springframework.security.core.userdetails.UsernameNotFoundException;
//import org.springframework.stereotype.Service;
//
///**
// * 处理用户密码验证
// * <AUTHOR>
// */
//@Service
//public class UserDetailsServiceImpl implements UserDetailsService {
//
//    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);
//
//    @Autowired
//    private ISysUserService userService;
//
//    @Override
//    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
//        SysUser sysUser = userService.selectUserByUserName(username);
//        if (StringUtils.isNull(sysUser)) {
//            log.info("登录用户：{} 不存在.", username);
//            throw new ServiceException(MessageUtils.message("user.not.exists"));
//        }
//        return null;
//    }
//}
