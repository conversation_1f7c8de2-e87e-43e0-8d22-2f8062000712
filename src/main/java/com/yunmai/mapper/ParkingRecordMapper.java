package com.yunmai.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunmai.domain.ParkingRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface ParkingRecordMapper extends BaseMapper<ParkingRecord> {

    // 自定义查询：按车牌号查询
    @Select("SELECT * FROM tb_ent_vh_inout WHERE vh_p_number = #{plateNumber}")
    List<ParkingRecord> selectByPlateNumber(String plateNumber);

    // 自定义查询：按时间范围查询
    @DS("pg1")
    List<ParkingRecord> selectByTimeRangePg1();

    // 自定义查询：按时间范围查询
    @DS("pg2")
    List<ParkingRecord> selectByTimeRangePg2();

    // 自定义查询：按时间范围查询
    @DS("pg3")
    List<ParkingRecord> selectByTimeRangePg3();

    // 按时间范围查询
    @DS("mysql")
    List<ParkingRecord> selectByTimeRangeMQ(@Param("startTime") String startTime,  @Param("endTime") String endTime);
}
