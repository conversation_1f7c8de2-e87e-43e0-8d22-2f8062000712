package com.yunmai.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付记录实体类
 */
@Data
@TableName("tb_payment_record")
@Schema(description = "支付记录实体")
public class PaymentRecord {

    @Schema(description = "主键ID(雪花算法)", example = "1357924680123456")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonProperty("id")
    private Long id;

    @Schema(description = "车牌号码", required = true, example = "粤B12345")
    @TableField("vehicle_no")
    @JsonProperty("vehicle_no")
    private String vehicleNo;

    @Schema(description = "停车记录ID", required = true, example = "1357924680123456")
    @TableField("parking_record_id")
    @JsonProperty("parking_record_id")
    private Long parkingRecordId;

    @Schema(description = "支付金额", required = true, example = "15.50")
    @TableField("payment_amount")
    @JsonProperty("payment_amount")
    private BigDecimal paymentAmount;

    @Schema(description = "应付金额", required = true, example = "15.50")
    @TableField("payable_amount")
    @JsonProperty("payable_amount")
    private BigDecimal payableAmount;

    @Schema(description = "支付方式(WECHAT-微信 ALIPAY-支付宝 BANK-银行卡)", required = true, example = "WECHAT")
    @TableField("payment_type")
    @JsonProperty("payment_type")
    private String paymentType;

    @Schema(description = "交易流水号", example = "TXN202312151430001")
    @TableField("transaction_id")
    @JsonProperty("transaction_id")
    private String transactionId;

    @Schema(description = "支付状态(0-待支付 1-支付成功 2-支付失败 3-已退款)", example = "1")
    @TableField("payment_status")
    @JsonProperty("payment_status")
    private Integer paymentStatus;

    @Schema(description = "是否访客车牌", example = "false")
    @TableField("is_visitor")
    @JsonProperty("is_visitor")
    private Boolean isVisitor;

    @Schema(description = "折扣率(访客折扣等)", example = "0.8")
    @TableField("discount_rate")
    @JsonProperty("discount_rate")
    private BigDecimal discountRate;

    @Schema(description = "停车时长(分钟)", example = "120")
    @TableField("parking_duration")
    @JsonProperty("parking_duration")
    private Integer parkingDuration;

    @Schema(description = "入场时间", type = "string", example = "2023-12-15 12:30:00")
    @TableField("entry_time")
    @JsonProperty("entry_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime entryTime;

    @Schema(description = "出场时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField("exit_time")
    @JsonProperty("exit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exitTime;

    @Schema(description = "支付时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField("payment_time")
    @JsonProperty("payment_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTime;

    @Schema(description = "出入口编号", example = "GATE001")
    @TableField("gate_no")
    @JsonProperty("gate_no")
    private String gateNo;

    @Schema(description = "备注信息", example = "访客车辆享受8折优惠")
    @TableField("remark")
    @JsonProperty("remark")
    private String remark;

    @Schema(description = "创建时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonProperty("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除标记(0-未删除 1-已删除)", example = "0")
    @TableField("deleted")
    @TableLogic
    @JsonProperty("deleted")
    private Integer deleted;
}
