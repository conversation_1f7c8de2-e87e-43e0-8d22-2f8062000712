package com.yunmai.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("kq_source")
public class KqSource {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String empid;

    private Date fDateTime;

    private String ftype;

    private String machNo;

    private String cardNo;
}
