package com.yunmai.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@Data
@TableName("tb_ent_vh_inout")
@Schema(description = "车辆进出记录实体")
public class ParkingRecord {

    @Schema(description = "主键ID(雪花算法)", example = "1357924680123456")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonProperty("id")
    private Long id;

    @Schema(description = "进出方向(0-进 1-出)", required = true, example = "0")
    @TableField("direction")
    @JsonProperty("direction")
    private Integer direction;

    @Schema(description = "客户ID", example = "1001")
    @TableField("customer_id")
    @JsonProperty("customer_id")
    private Integer customerId;

//    @Schema(description = "唯一会话ID", example = "session_123456")
//    @TableField("sid")
//    @JsonProperty("sid")
//    private String sid;

    @Schema(description = "车牌号码", required = true, example = "京A12345")
    @TableField("vh_p_number")
    @JsonProperty("vh_p_number")
    private String vhPNumber;

    @Schema(description = "车辆类型ID", example = "1")
    @TableField("vh_type_id")
    @JsonProperty("vh_type_id")
    private Integer vhTypeId;

    @Schema(description = "车辆分类ID", example = "2")
    @TableField("vh_cate_id")
    @JsonProperty("vh_cate_id")
    private Integer vhCateId;

    @Schema(description = "操作员ID", example = "5001")
    @TableField("operator_id")
    @JsonProperty("operator_id")
    private Integer operatorId;

    @Schema(description = "进出时间(带时区)", type = "string", example = "2023-08-15 14:30:00+08:00")
    @TableField("time")
    @JsonProperty("time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ssXXX")
    private OffsetDateTime time;

    @Schema(description = "车辆状态(0-正常 1-异常)", allowableValues = {"0", "1"}, example = "0")
    @TableField("state")
    @JsonProperty("state")
    private Integer state;

    @Schema(description = "状态异常说明", example = "车牌识别模糊")
    @TableField("state_ab_comment")
    @JsonProperty("state_ab_comment")
    private String stateAbComment;

    @Schema(description = "通道路径", example = "3")
    @TableField("channel_path")
    @JsonProperty("channel_path")
    private Integer channelPath;

    @Schema(description = "图片文件ID", example = "10001")
    @TableField("pic_file_id")
    @JsonProperty("pic_file_id")
    private Integer picFileId;

    @Schema(description = "预览图文件ID", example = "10002")
    @TableField("pic_p_file_id")
    @JsonProperty("pic_p_file_id")
    private Integer picPFileId;

//    @Schema(description = "图片文件路径", example = "/storage/2023/08/15/abc.jpg")
//    @TableField("pic_file_pathname")
//    @JsonProperty("pic_file_pathname")
//    private String picFilePathname;
//
//    @Schema(description = "预览图文件路径", example = "/storage/2023/08/15/abc_thumb.jpg")
//    @TableField("pic_p_file_pathname")
//    @JsonProperty("pic_p_file_pathname")
//    private String picPFilePathname;

    @Schema(description = "识别结果", example = "京A12345 置信度:98%")
    @TableField("reco_result")
    @JsonProperty("reco_result")
    private String recoResult;

    @Schema(description = "唯一标识符", example = "5f8d3b4a-1d9c-4f2e-b7a6-c5d9e8f0a1b2")
    @TableField("uid")
    @JsonProperty("uid")
    private String uid;

    // ==================== 支付相关字段 ====================

    @Schema(description = "支付状态(0-未支付 1-已支付)", example = "0")
    @TableField("payment_status")
    @JsonProperty("payment_status")
    private Integer paymentStatus;

    @Schema(description = "支付金额", example = "15.50")
    @TableField("payment_amount")
    @JsonProperty("payment_amount")
    private BigDecimal paymentAmount;

    @Schema(description = "支付时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField("payment_time")
    @JsonProperty("payment_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTime;

    @Schema(description = "支付方式(WECHAT-微信 ALIPAY-支付宝 BANK-银行卡)", example = "WECHAT")
    @TableField("payment_type")
    @JsonProperty("payment_type")
    private String paymentType;

    @Schema(description = "交易流水号", example = "TXN202312151430001")
    @TableField("transaction_id")
    @JsonProperty("transaction_id")
    private String transactionId;

    @Schema(description = "是否访客车牌", example = "false")
    @TableField("is_visitor")
    @JsonProperty("is_visitor")
    private Boolean isVisitor;

//    @Schema(description = "逻辑删除标记(0-未删除 1-已删除)", accessMode = Schema.AccessMode.READ_ONLY)
//    @TableLogic
//    @JsonProperty("deleted")
//    private Integer deleted;
}