package com.yunmai.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户账户实体类
 */
@Data
@TableName("tb_user_account")
@Schema(description = "用户账户实体")
public class UserAccount {

    @Schema(description = "主键ID(雪花算法)", example = "****************")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonProperty("id")
    private Long id;

    @Schema(description = "用户ID", required = true, example = "1001")
    @TableField("user_id")
    @JsonProperty("user_id")
    private Long userId;

    @Schema(description = "车牌号码", required = true, example = "粤B12345")
    @TableField("vehicle_no")
    @JsonProperty("vehicle_no")
    private String vehicleNo;

    @Schema(description = "账户余额", required = true, example = "100.50")
    @TableField("balance")
    @JsonProperty("balance")
    private BigDecimal balance;

    @Schema(description = "冻结金额", example = "0.00")
    @TableField("frozen_amount")
    @JsonProperty("frozen_amount")
    private BigDecimal frozenAmount;

    @Schema(description = "累计充值金额", example = "500.00")
    @TableField("total_recharge")
    @JsonProperty("total_recharge")
    private BigDecimal totalRecharge;

    @Schema(description = "累计消费金额", example = "399.50")
    @TableField("total_consume")
    @JsonProperty("total_consume")
    private BigDecimal totalConsume;

    @Schema(description = "账户状态(0-正常 1-冻结 2-注销)", example = "0")
    @TableField("account_status")
    @JsonProperty("account_status")
    private Integer accountStatus;

    @Schema(description = "用户姓名", example = "张三")
    @TableField("user_name")
    @JsonProperty("user_name")
    private String userName;

    @Schema(description = "手机号码", example = "***********")
    @TableField("phone_number")
    @JsonProperty("phone_number")
    private String phoneNumber;

    @Schema(description = "身份证号", example = "******************")
    @TableField("id_card")
    @JsonProperty("id_card")
    private String idCard;

    @Schema(description = "是否VIP用户", example = "false")
    @TableField("is_vip")
    @JsonProperty("is_vip")
    private Boolean isVip;

    @Schema(description = "VIP等级(0-普通 1-银卡 2-金卡 3-钻石)", example = "0")
    @TableField("vip_level")
    @JsonProperty("vip_level")
    private Integer vipLevel;

    @Schema(description = "最后充值时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField("last_recharge_time")
    @JsonProperty("last_recharge_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastRechargeTime;

    @Schema(description = "最后消费时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField("last_consume_time")
    @JsonProperty("last_consume_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastConsumeTime;

    @Schema(description = "创建时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", type = "string", example = "2023-12-15 14:30:00")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonProperty("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除标记(0-未删除 1-已删除)", example = "0")
    @TableField("deleted")
    @TableLogic
    @JsonProperty("deleted")
    private Integer deleted;

    /**
     * 获取可用余额（余额 - 冻结金额）
     */
    public BigDecimal getAvailableBalance() {
        BigDecimal balance = this.balance != null ? this.balance : BigDecimal.ZERO;
        BigDecimal frozen = this.frozenAmount != null ? this.frozenAmount : BigDecimal.ZERO;
        return balance.subtract(frozen);
    }

    /**
     * 判断余额是否充足
     */
    public boolean isBalanceSufficient(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return true;
        }
        return getAvailableBalance().compareTo(amount) >= 0;
    }

    /**
     * 获取账户状态描述
     */
    public String getAccountStatusDesc() {
        if (accountStatus == null) {
            return "未知";
        }
        switch (accountStatus) {
            case 0: return "正常";
            case 1: return "冻结";
            case 2: return "注销";
            default: return "未知";
        }
    }

    /**
     * 获取VIP等级描述
     */
    public String getVipLevelDesc() {
        if (vipLevel == null) {
            return "普通用户";
        }
        switch (vipLevel) {
            case 0: return "普通用户";
            case 1: return "银卡会员";
            case 2: return "金卡会员";
            case 3: return "钻石会员";
            default: return "普通用户";
        }
    }
}
