package com.yunmai;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = "com.yunmai",
        exclude = DataSourceAutoConfiguration.class)
@EnableScheduling
@MapperScan("com.yunmai.mapper")   //注意这里加上扫描地址
public class YMApiApplication {

    public static void main(String[] args) {
        // 增加配置验证
        ConfigurableApplicationContext ctx = SpringApplication.run(YMApiApplication.class, args);
        DynamicRoutingDataSource ds = ctx.getBean(DynamicRoutingDataSource.class);
        System.out.println("Active datasources: " + ds.getDataSources().keySet());
    }

}
