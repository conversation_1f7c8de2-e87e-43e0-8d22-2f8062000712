package com.yunmai.common.exception;

/**
 * 资源未找到异常类
 * 用于停车记录、支付记录等资源未找到的情况
 */
public class NotFoundException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 默认构造方法
     */
    public NotFoundException() {
        super();
        this.code = 404;
    }
    
    /**
     * 带消息的构造方法
     * @param message 错误消息
     */
    public NotFoundException(String message) {
        super(message);
        this.message = message;
        this.code = 404;
    }
    
    /**
     * 带消息和错误码的构造方法
     * @param message 错误消息
     * @param code 错误码
     */
    public NotFoundException(String message, Integer code) {
        super(message);
        this.message = message;
        this.code = code;
    }
    
    /**
     * 带消息和异常原因的构造方法
     * @param message 错误消息
     * @param cause 异常原因
     */
    public NotFoundException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = 404;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
}
