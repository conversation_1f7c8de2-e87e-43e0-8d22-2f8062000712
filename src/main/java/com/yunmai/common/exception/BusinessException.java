package com.yunmai.common.exception;

/**
 * 业务异常类
 * 用于停车缴费业务中的异常处理
 */
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 默认构造方法
     */
    public BusinessException() {
        super();
    }
    
    /**
     * 带消息的构造方法
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.message = message;
        this.code = 500;
    }
    
    /**
     * 带消息和错误码的构造方法
     * @param message 错误消息
     * @param code 错误码
     */
    public BusinessException(String message, Integer code) {
        super(message);
        this.message = message;
        this.code = code;
    }
    
    /**
     * 带消息和异常原因的构造方法
     * @param message 错误消息
     * @param cause 异常原因
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = 500;
    }
    
    /**
     * 带消息、错误码和异常原因的构造方法
     * @param message 错误消息
     * @param code 错误码
     * @param cause 异常原因
     */
    public BusinessException(String message, Integer code, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = code;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
}
