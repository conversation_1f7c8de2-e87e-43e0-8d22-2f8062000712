package com.yunmai.config.redis;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RLock;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Redisson分布式锁封装接口方法实现类
 * </p>
 *
 * <AUTHOR>
 * @Version: V1.0
 * @since 2020/5/14 5:34 下午
 */
@Component
public class DistributedLocker {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 使用分布式锁
     *
     * @param lockKey : redis分布式锁key
     * @return
     */
    public RLock lock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        return lock;
    }


    /**
     * 使用分布式锁 设置过期时间
     *
     * @param lockKey   : redis分布式锁key
     * @param leaseTime : 过期时间
     * @return
     */
    public RLock lock(String lockKey, int leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(leaseTime, TimeUnit.SECONDS);
        return lock;
    }


    /**
     * 使用分布式锁 设置过期时间
     *
     * @param lockKey : redis分布式锁key
     * @param unit    : 过期时间单位
     * @param timeout : 过期时间
     * @return
     */
    public RLock lock(String lockKey, TimeUnit unit, int timeout) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(timeout, unit);
        return lock;
    }

    /**
     * 尝试分布式锁，使用锁默认等待时间、超时时间。
     *
     * @param lockKey   : redis分布式锁key
     * @param unit      : 过期时间单位
     * @param waitTime  : 获取锁最长等待时间
     * @param leaseTime : 锁超时时间。超时后自动释放锁
     * @return
     */
    public boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            return false;
        }
    }

    /**
     * 尝试直接获取锁
     */
    public boolean tryLock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        return lock.tryLock();
    }

    /**
     * 直接获取锁，并设置锁超时时间 秒
     */
    public boolean tryLock(String lockKey, int leaseTime) {
        return tryLock(lockKey, TimeUnit.SECONDS, 0, leaseTime);
    }

    /**
     * 根据key释放锁
     *
     * @param lockKey : redis分布式锁key
     */
    public void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.unlock();
    }


    /**
     * 释放锁
     *
     * @param lock
     */
    public void unlock(RLock lock) {
        lock.unlock();
    }

    /**
     * 根据名称获取计数器
     *
     * @param name : 名称
     * @return 返回计数器
     */
    public RCountDownLatch getCountDownLatch(String name) {
        return redissonClient.getCountDownLatch(name);
    }

    /**
     * 更加名称获取信号量
     *
     * @param name : 名称
     * @return 返回信号量
     */
    public RSemaphore getSemaphore(String name) {
        return redissonClient.getSemaphore(name);
    }

}

