<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ymiots.mapper.ParkingRecordMapper">


    <select id="selectByTimeRangePg1" resultType="com.yunmai.domain.ParkingRecord">
        SELECT * FROM tb_ent_vh_inout
        WHERE time >= NOW() - INTERVAL '3 days';
    </select>

    <select id="selectByTimeRangePg2" resultType="com.yunmai.domain.ParkingRecord">
        SELECT * FROM tb_ent_vh_inout
        WHERE time >= NOW() - INTERVAL '3 days';
    </select>

    <select id="selectByTimeRangePg3" resultType="com.yunmai.domain.ParkingRecord">
        SELECT * FROM tb_ent_vh_inout
        WHERE time >= NOW() - INTERVAL '3 days';
    </select>

    <select id="selectByTimeRangeMQ" resultType="com.yunmai.domain.ParkingRecord">
        SELECT * FROM tb_ent_vh_inout
        <where>
            <!-- 时间范围条件 -->
            <if test="startTime != null and endTime != null and startTime != '' and endTime !='' ">
                AND time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="startTime != null and startTime != '' and (endTime == null or endTime =='')">
                AND time >= #{startTime}
            </if>
            <if test="(startTime == null or startTime =='') and endTime != null and endTime != ''">
                AND time &lt;= #{endTime}
            </if>
        </where>
        order by time desc
    </select>
</mapper>