server:
  port: 11011

spring:
  datasource:
    dynamic:
      primary: mysql  # 必须与下方数据源名称完全一致（包括大小写）
      datasource:
#        pg1:  # 数据源名称必须与@DS注解一致
#          enabled: false
#          url: ******************************************************
#          username: postgres
#          password: pass1
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          hikari:
#            pool-name: PG-Pool1
#            connection-timeout: 30000
#            maximum-pool-size: 5
#        pg2: # 数据源名称必须与@DS注解一致
#          enabled: false
#          url: *****************************************************
#          username: postgres
#          password: pass1
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          hikari:
#            pool-name: PG-Pool2
#            connection-timeout: 30000
#            maximum-pool-size: 5
#        pg3: # 数据源名称必须与@DS注解一致
#          enabled: false
#          url: *****************************************************
#          username: postgres
#          password: pass1
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          hikari:
#            pool-name: PG-Pool3
#            connection-timeout: 30000
#            maximum-pool-size: 5

        mysql:
          url: ******************************************************************************
          username: root
          password: "000000"
          driver-class-name: com.mysql.cj.jdbc.Driver
        
#        # 添加SQL Server数据源配置
#        sqlserver:  # 数据源名称，需要与@DS注解一致
#          enabled: false
#          url: ***********************************************************************************************
#          username: sa
#          password: '000000'
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          hikari:
#            pool-name: SQLServer-Pool
#            connection-timeout: 30000
#            maximum-pool-size: 5

  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: '000000'
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms




mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml  # XML映射文件路径
  type-aliases-package: com.ymiots.domain     # 实体类包路径
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 打印SQL日志
    map-underscore-to-camel-case: true        # 自动转换下划线命名
  global-config:
    db-config:
      id-type: assign_id                      # 全局ID策略（雪花ID）
      logic-delete-field: deleted             # 逻辑删除字段
      logic-not-delete-value: 0               # 未删除标记值
      logic-delete-value: 1                   # 已删除标记值

logging:
  level:
    com.ymiots.mapper: info  # 生产环境用 INFO，只记录关键操作
    org.springframework.jdbc.core.JdbcTemplate: warn  # 避免打印大量SQL

schedule:
  cron:
    task1: "0 */5 * * * ?"

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  #  # 请求前缀
  pathMapping: /dev-api

management:
  endpoints:
    web:
      exposure:
        include: loggers  # 暴露日志管理端点
  endpoint:
    loggers:
      enabled: true       # 启用日志级别修改功能