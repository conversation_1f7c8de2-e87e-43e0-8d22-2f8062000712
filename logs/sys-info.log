09:18:28.438 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 12692 (D:\YM-Api\target\classes started by Tan<PERSON> in D:\YM-Api)
09:18:28.444 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
09:18:30.076 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
09:18:30.076 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:30.076 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
09:18:30.358 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:30.505 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
09:18:30.911 [redisson-netty-2-13] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
09:18:30.934 [redisson-netty-2-17] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
09:18:31.217 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
09:18:31.432 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
09:18:31.432 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
09:18:31.432 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [mysql]
09:18:33.080 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
09:18:33.201 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 5.052 seconds (JVM running for 6.543)
09:18:33.550 [RMI TCP Connection(1)-192.168.9.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:02:17.777 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:02:17.778 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - mysql - Shutdown initiated...
10:02:17.792 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - mysql - Shutdown completed.
10:02:17.792 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:02:48.091 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 107768 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
10:02:48.092 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:02:49.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:02:49.553 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:02:49.553 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:02:49.813 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:02:49.942 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:02:50.276 [redisson-netty-2-22] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:02:50.296 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:02:50.498 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:02:50.639 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:02:50.639 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:02:50.639 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [mysql]
10:02:51.961 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:02:52.127 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 4.271 seconds (JVM running for 5.625)
10:02:53.012 [RMI TCP Connection(2)-192.168.9.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:08:32.754 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:08:32.754 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - mysql - Shutdown initiated...
10:08:32.763 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - mysql - Shutdown completed.
10:08:32.763 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:08:38.914 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 74496 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
10:08:38.915 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:08:40.414 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:08:40.415 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:08:40.415 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:08:40.761 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:08:40.899 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:08:41.248 [redisson-netty-2-22] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:08:41.270 [redisson-netty-2-16] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:08:41.489 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:08:41.616 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:08:41.617 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:08:41.617 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [mysql]
10:08:42.775 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:08:42.914 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 4.244 seconds (JVM running for 5.689)
10:08:43.129 [RMI TCP Connection(1)-192.168.9.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:21:26.923 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:21:26.925 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - mysql - Shutdown initiated...
11:21:26.930 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - mysql - Shutdown completed.
11:21:26.930 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:22:09.565 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 100860 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
11:22:09.567 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
11:22:11.215 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
11:22:11.217 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:22:11.217 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
11:22:11.997 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:22:12.157 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
11:22:12.562 [redisson-netty-2-15] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
11:22:12.623 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
11:22:12.911 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
11:22:13.063 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
11:22:13.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
11:22:13.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [mysql]
11:22:14.556 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
11:22:14.718 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 5.45 seconds (JVM running for 6.901)
11:22:15.212 [RMI TCP Connection(4)-192.168.9.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:25:34.633 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:25:34.633 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - mysql - Shutdown initiated...
11:25:34.640 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - mysql - Shutdown completed.
11:25:34.640 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
