09:10:09.898 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 44048 (D:\SyscPostgreSqlToMysql\target\classes started by <PERSON><PERSON> in D:\SyscPostgreSqlToMysql)
09:10:09.911 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,645] - No active profile set, falling back to 1 default profile: "default"
09:10:10.882 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
09:10:10.882 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:10:10.882 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.68]
09:10:10.984 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:10:11.582 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
09:10:12.196 [redisson-netty-2-14] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
09:10:12.216 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
09:10:12.883 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
09:10:12.922 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:10:12.922 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:10:13.191 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-11011"]
09:10:13.191 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:10:13.199 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-11011"]
09:10:13.203 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-11011"]
09:34:14.189 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 27580 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
09:34:14.189 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
09:34:15.185 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
09:34:15.185 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:34:15.185 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
09:34:15.286 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:34:15.878 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
09:34:16.274 [redisson-netty-2-21] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
09:34:16.297 [redisson-netty-2-20] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
09:34:16.877 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
09:34:16.918 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 2.929 seconds (JVM running for 3.911)
09:34:17.079 [RMI TCP Connection(1)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:35:08.963 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 27620 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
09:35:08.963 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
09:35:09.940 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
09:35:09.940 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:09.940 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
09:35:10.051 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:10.192 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:36:40.550 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 15356 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
09:36:40.552 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
09:36:41.541 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
09:36:41.541 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:36:41.541 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
09:36:41.650 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:36:41.812 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
09:36:41.891 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
09:36:41.891 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
09:36:41.891 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [mysql]
09:36:42.433 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
09:36:42.735 [redisson-netty-2-16] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
09:36:42.753 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
09:36:43.416 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
09:36:43.454 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.111 seconds (JVM running for 3.919)
09:36:44.080 [RMI TCP Connection(2)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:37:03.563 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:39:46.009 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:40:00.014 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:41:00.006 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:42:00.013 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:43:00.005 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:44:00.009 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:45:00.012 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:46:00.008 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:47:00.003 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:48:00.014 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:49:00.006 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:50:00.012 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:50:24.962 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 22800 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
09:50:24.969 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
09:50:25.911 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
09:50:25.911 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:50:25.911 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
09:50:26.003 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:50:26.158 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
09:50:26.293 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
09:50:26.293 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
09:50:26.464 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
09:50:26.465 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
09:50:26.527 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
09:50:26.533 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
09:50:26.597 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
09:50:26.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
09:50:26.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
09:50:26.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
09:50:26.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
09:50:26.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
09:50:27.204 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
09:50:27.500 [redisson-netty-2-13] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
09:50:27.521 [redisson-netty-2-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
09:50:28.126 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
09:50:28.157 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.389 seconds (JVM running for 4.085)
09:50:28.820 [RMI TCP Connection(2)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:51:00.025 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:51:00.295 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:52:00.017 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:52:00.112 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:53:00.011 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:53:00.046 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:54:00.022 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:54:00.054 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:55:00.018 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:55:00.046 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:56:00.010 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:56:00.042 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:57:00.011 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:57:00.042 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:58:00.016 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:58:00.032 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
09:59:00.012 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
09:59:00.026 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:00:00.010 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:00:00.042 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:01:00.014 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:01:00.046 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:02:00.012 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:02:00.037 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:03:00.006 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:03:02.429 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 42544 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:03:02.433 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:03:03.424 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:03:03.425 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:03:03.425 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:03:03.533 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:03:03.696 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:03:03.824 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:03:03.825 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:03:03.901 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:03:03.903 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:03:03.964 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:03:03.965 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:03:04.028 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:03:04.030 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:03:04.030 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:03:04.030 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:03:04.030 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:03:04.030 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:03:04.512 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:03:04.848 [redisson-netty-2-22] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:03:04.865 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:03:05.462 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:03:05.493 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.271 seconds (JVM running for 4.004)
10:03:06.291 [RMI TCP Connection(3)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:04:27.120 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 11832 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:04:27.122 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:04:28.097 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:04:28.098 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:28.098 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:04:28.205 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:28.354 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:04:28.477 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:04:28.479 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:04:29.577 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:04:29.579 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:04:29.656 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:04:29.658 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:04:29.722 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:04:29.722 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:04:29.722 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:04:29.722 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:04:29.722 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:04:29.722 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:04:30.169 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:04:30.475 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:04:30.507 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:04:31.121 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:04:31.153 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 4.239 seconds (JVM running for 5.25)
10:04:31.524 [RMI TCP Connection(3)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:05:00.801 [http-nio-11011-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:08:00.016 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:08:00.251 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:09:00.006 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:09:00.032 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:10:00.002 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:10:00.032 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:10:17.629 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 16972 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:10:17.631 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:10:18.584 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:10:18.585 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:10:18.585 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:10:18.713 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:10:18.874 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:10:19.003 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:10:19.004 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:10:19.080 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:10:19.081 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:10:19.142 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:10:19.143 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:10:19.228 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:10:19.228 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:10:19.228 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:10:19.228 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:10:19.228 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:10:19.228 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:10:19.702 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:10:20.030 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:10:20.049 [redisson-netty-2-20] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:10:20.668 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:10:20.778 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.347 seconds (JVM running for 4.193)
10:10:21.309 [RMI TCP Connection(1)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:10:41.701 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 25804 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:10:41.704 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:10:42.681 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:10:42.681 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:10:42.681 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:10:42.806 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:10:43.007 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:10:43.137 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:10:43.139 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:10:43.256 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:10:43.258 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:10:43.316 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:10:43.317 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:10:43.383 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:10:43.383 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:10:43.383 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:10:43.383 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:10:43.383 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:10:43.383 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:10:43.920 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:10:44.221 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:10:44.243 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:10:44.826 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:10:44.932 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.428 seconds (JVM running for 4.122)
10:10:45.151 [RMI TCP Connection(4)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:10:48.375 [http-nio-11011-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:11:00.014 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:11:00.180 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:12:00.003 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:12:00.139 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:13:00.017 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:13:00.048 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:14:00.012 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:14:00.043 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:15:00.008 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:15:00.045 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:15:19.503 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 6344 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:15:19.506 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:15:20.452 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:15:20.454 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:15:20.454 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:15:20.564 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:15:20.716 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:15:20.841 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:15:20.843 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:15:20.909 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:15:20.910 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:15:20.976 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:15:20.977 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:15:21.039 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:15:21.039 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:15:21.039 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:15:21.039 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:15:21.039 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:15:21.040 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:15:21.482 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:15:21.795 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:15:21.815 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:15:22.384 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:15:22.484 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.179 seconds (JVM running for 3.881)
10:15:22.948 [RMI TCP Connection(2)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:15:27.523 [http-nio-11011-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:16:00.022 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:16:00.147 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:16:43.910 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:16:43.910 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - pg2 - Shutdown initiated...
10:16:43.912 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - pg2 - Shutdown completed.
10:16:43.913 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - mysql - Shutdown initiated...
10:16:43.917 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - mysql - Shutdown completed.
10:16:43.918 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - pg1 - Shutdown initiated...
10:16:43.920 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - pg1 - Shutdown completed.
10:16:43.920 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - pg3 - Shutdown initiated...
10:16:43.921 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - pg3 - Shutdown completed.
10:16:43.921 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:16:47.353 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 27208 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:16:47.356 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:16:48.294 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:16:48.295 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:16:48.295 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:16:48.395 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:16:48.543 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:16:48.665 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:16:48.667 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:16:48.838 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:16:48.840 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:16:48.925 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:16:48.926 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:16:48.987 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:16:48.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:16:48.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:16:48.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:16:48.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:16:48.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:16:49.468 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:16:49.836 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:16:49.856 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:16:50.497 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:16:50.614 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.455 seconds (JVM running for 4.205)
10:16:51.416 [RMI TCP Connection(3)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:17:00.021 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:17:00.166 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:17:23.799 [http-nio-11011-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:18:00.014 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:18:00.162 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:19:00.943 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 22732 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:19:00.944 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:19:01.911 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:19:01.911 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:19:01.912 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:19:02.010 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:19:02.161 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:19:02.281 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:19:02.283 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:19:02.460 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:19:02.462 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:19:02.526 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:19:02.527 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:19:02.589 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:19:02.590 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:19:02.590 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:19:02.590 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:19:02.590 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:19:02.590 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:19:03.065 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:19:03.362 [redisson-netty-2-13] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:19:03.396 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:19:04.017 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:19:04.126 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.385 seconds (JVM running for 4.173)
10:19:04.903 [RMI TCP Connection(1)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:06.980 [http-nio-11011-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:19:33.364 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 31760 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:19:33.367 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:19:34.359 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:19:34.360 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:19:34.360 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:19:34.476 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:19:34.626 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:19:34.747 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:19:34.749 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:19:34.828 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:19:34.829 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:19:34.889 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:19:34.891 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:19:34.954 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:19:34.954 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:19:34.954 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:19:34.954 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:19:34.955 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:19:34.955 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:19:35.404 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:19:35.698 [redisson-netty-2-13] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:19:35.716 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:19:36.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:19:36.414 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.252 seconds (JVM running for 4.051)
10:19:36.618 [RMI TCP Connection(1)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:43.793 [http-nio-11011-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:20:00.015 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:20:00.088 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:21:00.010 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:21:00.075 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:22:00.010 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:22:00.041 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:23:00.010 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:23:00.043 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:24:00.011 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:24:00.027 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:25:00.030 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:25:00.046 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:26:00.013 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:26:00.028 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:27:00.008 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:27:00.033 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:28:00.003 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:28:00.045 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:28:21.600 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 19364 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
10:28:21.600 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
10:28:22.570 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
10:28:22.570 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:28:22.570 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
10:28:22.682 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:28:22.841 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
10:28:22.952 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
10:28:22.952 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
10:28:23.032 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
10:28:23.032 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
10:28:23.096 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
10:28:23.096 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
10:28:23.160 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
10:28:23.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
10:28:23.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
10:28:23.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
10:28:23.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
10:28:23.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
10:28:23.606 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
10:28:23.897 [redisson-netty-2-22] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:28:23.910 [redisson-netty-2-16] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:28:24.492 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
10:28:24.587 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.196 seconds (JVM running for 4.001)
10:28:24.778 [RMI TCP Connection(1)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:29:00.020 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:29:00.221 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:30:00.015 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:30:00.040 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:31:00.016 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:31:00.031 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:32:00.017 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:32:00.051 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:33:00.009 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:33:00.025 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:34:00.010 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:34:00.026 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:35:00.009 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:35:00.027 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:36:00.014 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:36:00.030 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:37:00.019 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:37:00.034 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:38:00.011 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:38:00.053 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:39:00.005 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:39:00.024 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:40:00.011 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:40:00.029 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:41:00.010 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:41:00.026 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:42:00.009 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:42:00.030 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:43:00.012 [TaskScheduler-8] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:43:00.033 [TaskScheduler-8] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:44:00.005 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:44:00.042 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:45:00.009 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:45:00.034 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:46:00.003 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:46:00.019 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:47:00.010 [TaskScheduler-10] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:47:00.026 [TaskScheduler-10] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:48:00.021 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:48:00.037 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:49:00.018 [TaskScheduler-11] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:49:00.034 [TaskScheduler-11] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:50:00.020 [TaskScheduler-11] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:50:00.035 [TaskScheduler-11] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:51:00.018 [TaskScheduler-12] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:51:00.052 [TaskScheduler-12] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:52:00.009 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:52:00.025 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:53:00.008 [TaskScheduler-13] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:53:00.039 [TaskScheduler-13] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:54:00.014 [TaskScheduler-13] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:54:00.036 [TaskScheduler-13] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:55:00.010 [TaskScheduler-14] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:55:00.035 [TaskScheduler-14] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:56:00.008 [TaskScheduler-14] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:56:00.027 [TaskScheduler-14] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:57:00.016 [TaskScheduler-15] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:57:00.046 [TaskScheduler-15] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:58:00.019 [TaskScheduler-8] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:58:00.062 [TaskScheduler-8] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
10:59:00.012 [TaskScheduler-16] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
10:59:00.078 [TaskScheduler-16] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:00:00.009 [TaskScheduler-16] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:00:00.030 [TaskScheduler-16] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:01:00.011 [TaskScheduler-17] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:01:00.040 [TaskScheduler-17] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:02:00.007 [TaskScheduler-17] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:02:00.021 [TaskScheduler-17] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:03:00.009 [TaskScheduler-17] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:03:00.040 [TaskScheduler-17] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:04:00.019 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:04:00.035 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:05:00.022 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:05:00.042 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:06:00.010 [TaskScheduler-10] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:06:00.031 [TaskScheduler-10] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:07:00.010 [TaskScheduler-20] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:07:00.038 [TaskScheduler-20] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:08:00.006 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:08:00.017 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:08:33.120 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 24724 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
11:08:33.121 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
11:08:34.089 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
11:08:34.089 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:08:34.089 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
11:08:34.203 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:08:34.358 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
11:08:34.475 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
11:08:34.475 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
11:08:34.558 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
11:08:34.561 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
11:08:34.619 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
11:08:34.619 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
11:08:34.688 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
11:08:34.688 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
11:08:34.688 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
11:08:34.688 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
11:08:34.688 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
11:08:34.688 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
11:08:35.138 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
11:08:35.421 [redisson-netty-2-22] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
11:08:35.440 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
11:08:36.064 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
11:08:36.162 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.255 seconds (JVM running for 3.96)
11:08:36.452 [RMI TCP Connection(2)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:09:00.016 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:09:00.204 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:10:00.007 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:10:00.071 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:11:00.010 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:11:00.041 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:12:00.004 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:12:00.033 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:13:00.016 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:13:00.037 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:14:00.006 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:14:00.025 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:15:00.015 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:15:00.031 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:15:42.741 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 10108 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
11:15:42.745 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
11:15:43.788 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
11:15:43.788 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:15:43.788 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
11:15:43.897 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:15:44.052 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
11:15:44.195 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
11:15:44.197 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
11:15:44.268 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
11:15:44.268 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
11:15:44.328 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
11:15:44.328 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
11:15:44.404 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
11:15:44.404 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
11:15:44.404 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
11:15:44.404 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
11:15:44.404 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
11:15:44.404 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
11:15:44.855 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
11:15:45.148 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
11:15:45.179 [redisson-netty-2-20] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
11:15:45.760 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
11:15:45.863 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.32 seconds (JVM running for 4.194)
11:15:46.589 [RMI TCP Connection(3)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:16:00.014 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:16:00.136 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:16:48.403 [http-nio-11011-exec-2] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
11:17:00.003 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:17:00.106 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:18:00.002 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:18:00.025 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:19:00.004 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:19:00.053 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:20:00.006 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:20:00.057 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:20:52.817 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 40020 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
11:20:52.817 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
11:20:53.835 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
11:20:53.835 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:20:53.835 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
11:20:53.957 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:20:54.115 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
11:20:54.254 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
11:20:54.254 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
11:20:54.314 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
11:20:54.316 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
11:20:54.374 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
11:20:54.375 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
11:20:54.437 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
11:20:54.437 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
11:20:54.437 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
11:20:54.437 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
11:20:54.437 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
11:20:54.437 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
11:20:54.900 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
11:20:55.193 [redisson-netty-2-13] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
11:20:55.228 [redisson-netty-2-22] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
11:20:55.823 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
11:20:55.854 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,350] - pg2 - Shutdown initiated...
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,352] - pg2 - Shutdown completed.
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,350] - mysql - Shutdown initiated...
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,352] - mysql - Shutdown completed.
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,350] - pg1 - Shutdown initiated...
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,352] - pg1 - Shutdown completed.
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,350] - pg3 - Shutdown initiated...
11:20:55.854 [main] INFO  c.z.h.HikariDataSource - [close,352] - pg3 - Shutdown completed.
11:20:55.854 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:20:55.854 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-11011"]
11:20:55.854 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:20:55.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-11011"]
11:20:55.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-11011"]
11:21:00.017 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:21:00.061 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:21:19.264 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarting,55] - Starting SyscPostgreSqlToMysqlApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 42796 (D:\SyscPostgreSqlToMysql\target\classes started by Tanph in D:\SyscPostgreSqlToMysql)
11:21:19.264 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
11:21:20.294 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
11:21:20.294 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:21:20.294 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
11:21:20.410 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:21:20.562 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
11:21:20.690 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
11:21:20.690 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
11:21:20.753 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
11:21:20.753 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
11:21:20.819 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
11:21:20.819 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
11:21:20.881 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
11:21:20.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
11:21:20.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
11:21:20.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
11:21:20.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
11:21:20.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
11:21:21.429 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
11:21:21.724 [redisson-netty-2-13] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
11:21:21.740 [redisson-netty-2-20] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
11:21:22.315 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
11:21:22.420 [main] INFO  c.y.SyscPostgreSqlToMysqlApplication - [logStarted,61] - Started SyscPostgreSqlToMysqlApplication in 3.355 seconds (JVM running for 4.076)
11:21:22.531 [RMI TCP Connection(3)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:22:00.008 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:22:00.121 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:23:00.020 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:23:00.037 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:24:00.018 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:24:00.041 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:25:00.016 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:25:00.041 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:26:00.013 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:26:00.112 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:27:00.005 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:27:00.037 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
11:27:02.753 [http-nio-11011-exec-9] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
11:28:00.010 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
11:28:00.055 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:47:11.097 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 28272 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
13:47:11.100 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
13:47:12.091 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
13:47:12.091 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:47:12.091 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
13:47:12.222 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:47:12.378 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
13:47:12.497 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
13:47:12.498 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
13:47:12.662 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
13:47:12.664 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
13:47:12.727 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
13:47:12.728 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
13:47:12.791 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
13:47:12.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
13:47:12.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
13:47:12.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
13:47:12.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
13:47:12.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
13:47:13.249 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
13:47:13.578 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
13:47:13.604 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
13:47:14.235 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
13:47:14.343 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 3.45 seconds (JVM running for 4.194)
13:47:15.259 [RMI TCP Connection(7)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:48:00.016 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:48:00.136 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:49:00.017 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:49:00.046 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:50:00.011 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:50:00.043 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:51:00.012 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:51:00.046 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:52:00.004 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:52:00.034 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:53:00.014 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:53:00.064 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:54:00.002 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:54:00.035 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:55:00.006 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:55:00.027 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:56:00.008 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:56:00.031 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:57:00.003 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:57:00.052 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:58:00.015 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:58:00.040 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
13:59:00.006 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
13:59:00.027 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:00:00.005 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:00:00.020 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:01:00.008 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:01:00.025 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:02:00.002 [TaskScheduler-8] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:02:00.017 [TaskScheduler-8] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:03:00.007 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:03:00.040 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:04:00.014 [TaskScheduler-9] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:04:00.036 [TaskScheduler-9] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:05:00.008 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:05:00.030 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:06:00.016 [TaskScheduler-10] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:06:00.047 [TaskScheduler-10] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:07:00.011 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:07:00.027 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:08:00.013 [TaskScheduler-11] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:08:00.038 [TaskScheduler-11] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:09:00.014 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:09:00.026 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:10:00.004 [TaskScheduler-12] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:10:00.067 [TaskScheduler-12] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:11:00.013 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:11:00.027 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:12:00.005 [TaskScheduler-13] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:12:00.027 [TaskScheduler-13] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:13:00.008 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:13:00.043 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:14:00.015 [TaskScheduler-14] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:14:00.047 [TaskScheduler-14] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:15:00.010 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:15:00.034 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
14:16:00.016 [TaskScheduler-15] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:43:38.531 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 2548 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
14:43:38.533 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
14:43:39.563 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
14:43:39.563 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:43:39.564 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
14:43:39.677 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:43:39.835 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
14:43:39.954 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
14:43:39.956 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
14:43:40.141 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
14:43:40.142 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
14:43:40.199 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
14:43:40.201 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
14:43:40.263 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
14:43:40.263 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
14:43:40.263 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
14:43:40.263 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
14:43:40.263 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
14:43:40.263 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [4] datasource,primary datasource named [mysql]
14:43:40.757 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
14:43:41.059 [redisson-netty-2-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
14:43:41.082 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
14:43:41.699 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
14:43:41.816 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 3.497 seconds (JVM running for 4.398)
14:43:42.325 [RMI TCP Connection(3)-192.168.9.74] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:44:00.015 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
14:44:00.115 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
