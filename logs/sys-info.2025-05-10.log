17:54:33.909 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 50412 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
17:54:33.918 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
17:54:34.963 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
17:54:34.963 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:54:34.964 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
17:54:35.076 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:54:35.231 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
17:54:35.475 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
17:54:35.477 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
17:54:36.086 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
17:54:36.088 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
17:54:36.139 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
17:54:36.141 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
17:54:36.205 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
17:54:36.207 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - sqlserver - Starting...
17:55:08.442 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:59:16.883 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 52260 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
17:59:16.885 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
17:59:17.957 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
17:59:17.957 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:59:17.958 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
17:59:18.084 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:59:18.244 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
17:59:18.405 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
17:59:18.407 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
17:59:18.608 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
17:59:18.610 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
17:59:18.661 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
17:59:18.663 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
17:59:18.728 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
17:59:18.729 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - sqlserver - Starting...
17:59:49.760 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
18:07:36.896 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 46740 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
18:07:36.897 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
18:07:37.906 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
18:07:37.906 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:07:37.906 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
18:07:38.031 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:07:38.186 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
18:07:38.335 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
18:07:38.336 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
18:07:38.508 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
18:07:38.509 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
18:07:38.565 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
18:07:38.566 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
18:07:38.628 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
18:07:38.630 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - sqlserver - Starting...
18:08:09.670 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
18:08:51.801 [main] INFO  c.y.YMApiApplication - [logStarting,55] - Starting YMApiApplication using Java 11.0.2 on LAPTOP-I3RJCDS4 with PID 5892 (D:\YM-Api\target\classes started by Tanph in D:\YM-Api)
18:08:51.803 [main] INFO  c.y.YMApiApplication - [logStartupProfileInfo,680] - No active profile set, falling back to 1 default profile: "default"
18:08:52.842 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-11011"]
18:08:52.842 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:08:52.843 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
18:08:52.964 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:08:53.121 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg1 - Starting...
18:08:53.273 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg1 - Start completed.
18:08:53.275 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg2 - Starting...
18:08:53.357 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg2 - Start completed.
18:08:53.359 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - pg3 - Starting...
18:08:53.407 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - pg3 - Start completed.
18:08:53.408 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - mysql - Starting...
18:08:53.471 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - mysql - Start completed.
18:08:53.472 [main] INFO  c.z.h.HikariDataSource - [<init>,80] - sqlserver - Starting...
18:08:53.587 [main] INFO  c.z.h.HikariDataSource - [<init>,82] - sqlserver - Start completed.
18:08:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [sqlserver] success
18:08:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg2] success
18:08:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [mysql] success
18:08:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg1] success
18:08:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [pg3] success
18:08:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [5] datasource,primary datasource named [mysql]
18:08:54.029 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.16.0
18:08:54.358 [redisson-netty-2-23] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$run$0,166] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
18:08:54.377 [redisson-netty-2-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$run$0,166] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
18:08:54.935 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-11011"]
18:08:55.044 [main] INFO  c.y.YMApiApplication - [logStarted,61] - Started YMApiApplication in 3.441 seconds (JVM running for 4.215)
18:08:55.495 [RMI TCP Connection(3)-192.168.9.168] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:09:00.016 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:09:00.314 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:10:00.015 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:10:00.071 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:11:00.009 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:11:00.031 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:12:00.003 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:12:00.032 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:13:00.008 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:13:00.036 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:14:00.011 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:14:00.033 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:15:00.026 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:15:00.054 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:16:00.014 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:16:00.043 [TaskScheduler-1] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:17:00.015 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:17:00.046 [TaskScheduler-5] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:18:00.007 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:18:00.037 [TaskScheduler-3] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:19:00.010 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:19:00.038 [TaskScheduler-6] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:20:00.002 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:20:00.025 [TaskScheduler-2] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:21:00.005 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:21:00.028 [TaskScheduler-7] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
18:22:00.015 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,38] - 开始执行停车记录同步...
18:22:00.034 [TaskScheduler-4] INFO  c.y.t.ParkingSyncScheduler - [scheduledSync,58] - 同步 0 条记录,状态false
